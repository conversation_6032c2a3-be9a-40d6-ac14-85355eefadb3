{"name": "reactlib", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android --mode officialDebug", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@bravemobile/react-native-code-push": "^10.0.0-beta.5", "@react-native/new-app-screen": "0.80.0", "@shopify/flash-list": "^2.0.0-rc.11", "axios": "^1.10.0", "lottie-react-native": "^7.2.4", "moti": "^0.30.0", "react": "19.0.0", "react-content-loader": "^7.1.1", "react-native": "^0.79.4", "react-native-auto-skeleton": "^0.1.26", "react-native-dotenv": "^3.4.11", "react-native-linear-gradient": "^2.8.3", "rtn-weee-module": "file:./modules/rtn-weee-module"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/node": "^24.0.3", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "ts-node": "^10.9.2", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "codegenConfig": {"name": "RTNWeeeRouteModuleSpec", "type": "all", "jsSrcsDir": "specs", "android": {"javaPackageName": "com.sayweee.react.spec"}}}